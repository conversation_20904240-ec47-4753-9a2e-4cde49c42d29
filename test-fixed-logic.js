// Test the fixed API config selection logic from ChatTextArea.tsx
const modesModleNotSuportList = {
  architect: ["zhanlu-r1", "jiutian-75b"],
  code: ["zhanlu-r1", "jiutian-75b"],
  "algorithm-practice": ["zhanlu-r1", "jiutian-75b"],
  simple: [], // zhanlu-r1模型能力不足，仅在智能问答模式下使用
  test: ["zhanlu-r1", "jiutian-75b"],
  "project-fix": ["zhanlu-r1", "jiutian-75b"],
  sast: ["zhanlu-r1", "jiutian-75b"],
  "code-review": ["zhanlu-r1", "jiutian-75b"],
  readme: ["zhanlu-r1", "jiutian-75b"],
}

// Simulate the FIXED API config options logic from ChatTextArea.tsx
function processApiConfigOptionsFixed(configs, mode) {
  return configs.map((config) => {
    if ("zhanluModelId" in config && config.zhanluModelId && modesModleNotSuportList[mode]) {
      return {
        ...config,
        disabled: modesModleNotSuportList[mode].includes(config.zhanluModelId),
      }
    } else {
      return {
        ...config,
        disabled: false,
      }
    }
  })
}

// Test data
const testConfigs = [
  { id: "1", name: "GPT-4 Config", zhanluModelId: "gpt-4" },
  { id: "2", name: "Zhanlu-R1 Config", zhanluModelId: "zhanlu-r1" },
  { id: "3", name: "Jiutian-75B Config", zhanluModelId: "jiutian-75b" },
  { id: "4", name: "Claude Config", zhanluModelId: "claude-3-5-sonnet-20241022" },
  { id: "5", name: "Config without model" }, // No zhanluModelId
]

const testModes = ["simple", "architect", "code"]

console.log("Testing FIXED ChatTextArea API Config Selection Logic:")
console.log("=".repeat(55))

testModes.forEach(mode => {
  console.log(`\nMode: ${mode}`)
  console.log("-".repeat(30))
  
  const processedConfigs = processApiConfigOptionsFixed(testConfigs, mode)
  
  processedConfigs.forEach(config => {
    const status = config.disabled ? "❌ DISABLED" : "✅ ENABLED"
    const modelInfo = config.zhanluModelId ? ` (${config.zhanluModelId})` : " (no model)"
    console.log(`${config.name}${modelInfo}: ${status}`)
  })
})

console.log("\n" + "=".repeat(55))
console.log("Expected behavior:")
console.log("- In 'simple' mode: All configs should be ENABLED")
console.log("- In 'architect' and 'code' modes: zhanlu-r1 and jiutian-75b configs should be DISABLED")
console.log("- Configs without zhanluModelId should always be ENABLED")
